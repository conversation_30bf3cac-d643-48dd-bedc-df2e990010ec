import { BPlusTree } from './bPlusTree';

// 简单的测试断言函数
function assert(condition: boolean, message: string): void {
  if (!condition) {
    throw new Error(`断言失败: ${message}`);
  }
}

// 验证 B+ 树重构后的功能
export function validateBPlusTreeRefactoring(): void {
  console.log('🚀 开始验证 B+ 树重构后的功能...');

  try {
    // 测试 1: 基本参数计算
    console.log('📊 测试 1: 基本参数计算');
    const tree = new BPlusTree(3);
    assert(tree.getOrder() === 3, '阶数应该为 3');
    assert(tree.getMaxKeys() === 2, '最大键数应该为 2');
    assert(tree.getMinKeys() === 1, '最小键数应该为 1');
    assert(tree.getSplitIndex() === 1, '分裂索引应该为 1');
    console.log('✅ 基本参数计算测试通过');

    // 测试 2: 纯函数式插入
    console.log('📊 测试 2: 纯函数式插入');
    const insertResult1 = tree.insertPure(10);
    assert(insertResult1.success === true, '插入应该成功');
    assert(tree.find(10) === true, '应该能找到插入的键');

    const insertResult2 = tree.insertPure(10);
    assert(insertResult2.success === false, '重复插入应该失败');
    assert(insertResult2.error === '键 10 已存在', '应该返回正确的错误信息');
    console.log('✅ 纯函数式插入测试通过');

    // 测试 3: 多键插入和分裂
    console.log('📊 测试 3: 多键插入和分裂');
    const keys = [20, 30, 40, 50];
    keys.forEach(key => {
      const result = tree.insertPure(key);
      assert(result.success === true, `插入键 ${key} 应该成功`);
    });

    const allKeys = tree.getAllKeys();
    assert(allKeys.length === 5, '应该有 5 个键');
    assert(JSON.stringify(allKeys) === JSON.stringify([10, 20, 30, 40, 50]), '键应该按顺序排列');
    console.log('✅ 多键插入和分裂测试通过');

    // 测试 4: 纯函数式删除
    console.log('📊 测试 4: 纯函数式删除');
    const deleteResult1 = tree.deletePure(30);
    assert(deleteResult1.success === true, '删除应该成功');
    assert(tree.find(30) === false, '删除的键应该不存在');

    const deleteResult2 = tree.deletePure(100);
    assert(deleteResult2.success === false, '删除不存在的键应该失败');
    assert(deleteResult2.error === '键 100 不存在', '应该返回正确的错误信息');

    const remainingKeys = tree.getAllKeys();
    assert(JSON.stringify(remainingKeys) === JSON.stringify([10, 20, 40, 50]), '剩余键应该正确');
    console.log('✅ 纯函数式删除测试通过');

    // 测试 4.5: 删除操作后父节点索引键更新测试
    console.log('📊 测试 4.5: 删除操作后父节点索引键更新');

    // 创建一个新的树来测试索引键更新
    const indexTree = new BPlusTree(3);

    // 插入足够的键来创建多层结构
    const testKeys = [10, 20, 30, 40, 50, 60, 70, 80, 90];
    testKeys.forEach(key => {
      const result = indexTree.insertPure(key);
      assert(result.success === true, `插入键 ${key} 应该成功`);
    });

    // 获取初始状态
    const initialNodes = indexTree.getAllNodes();
    assert(initialNodes.length > 1, '应该有多个节点形成树结构');

    // 测试删除第一个键后索引键更新
    const deleteResult = indexTree.deletePure(10);
    assert(deleteResult.success === true, '删除第一个键应该成功');

    // 验证树结构仍然正确
    const afterDeleteKeys = indexTree.getAllKeys();
    assert(afterDeleteKeys.length === testKeys.length - 1, '删除后键数应该减少1');
    assert(!afterDeleteKeys.includes(10), '删除的键不应该存在');
    assert(JSON.stringify(afterDeleteKeys) === JSON.stringify([20, 30, 40, 50, 60, 70, 80, 90]), '剩余键应该正确排序');

    // 验证内部节点的索引键是否正确更新
    const afterDeleteNodes = indexTree.getAllNodes();
    const afterDeleteInternalNodes = afterDeleteNodes.filter(n => !n.isLeaf);

    // 检查所有内部节点的索引键是否指向正确的子树最小值
    afterDeleteInternalNodes.forEach(internalNode => {
      const internal = internalNode as import('./bPlusTree').InternalNode;
      internal.children.forEach((childId, index) => {
        if (childId && index > 0) {
          const child = indexTree.getAllNodes().find(n => n.id === childId);
          if (child && child.keys.length > 0) {
            const expectedKey = child.keys[0];
            const actualKey = internal.keys[index - 1];
            assert(actualKey === expectedKey,
              `内部节点索引键 ${actualKey} 应该等于子节点第一个键 ${expectedKey}`);
          }
        }
      });
    });

    // 测试借位操作后的索引键更新
    // 删除更多键来触发借位操作
    const keysToDelete = [20, 30, 40];
    keysToDelete.forEach(key => {
      const result = indexTree.deletePure(key);
      assert(result.success === true, `删除键 ${key} 应该成功`);
    });

    // 再次验证索引键的正确性
    const finalNodes = indexTree.getAllNodes();
    const finalInternalNodes = finalNodes.filter(n => !n.isLeaf);

    finalInternalNodes.forEach(internalNode => {
      const internal = internalNode as import('./bPlusTree').InternalNode;
      internal.children.forEach((childId, index) => {
        if (childId && index > 0) {
          const child = indexTree.getAllNodes().find(n => n.id === childId);
          if (child && child.keys.length > 0) {
            const expectedKey = child.keys[0];
            const actualKey = internal.keys[index - 1];
            assert(actualKey === expectedKey,
              `借位后内部节点索引键 ${actualKey} 应该等于子节点第一个键 ${expectedKey}`);
          }
        }
      });
    });

    // 测试合并操作后的索引键维护
    // 继续删除键来触发合并操作
    const remainingKeysForMerge = indexTree.getAllKeys();
    if (remainingKeysForMerge.length > 3) {
      // 删除更多键来触发合并
      const moreKeysToDelete = remainingKeysForMerge.slice(0, Math.floor(remainingKeysForMerge.length / 2));
      moreKeysToDelete.forEach(key => {
        const result = indexTree.deletePure(key);
        assert(result.success === true, `删除键 ${key} 应该成功`);
      });

      // 验证合并后的索引键正确性
      const mergedNodes = indexTree.getAllNodes();
      const mergedInternalNodes = mergedNodes.filter(n => !n.isLeaf);

      mergedInternalNodes.forEach(internalNode => {
        const internal = internalNode as import('./bPlusTree').InternalNode;
        internal.children.forEach((childId, index) => {
          if (childId && index > 0) {
            const child = indexTree.getAllNodes().find(n => n.id === childId);
            if (child && child.keys.length > 0) {
              const expectedKey = child.keys[0];
              const actualKey = internal.keys[index - 1];
              assert(actualKey === expectedKey,
                `合并后内部节点索引键 ${actualKey} 应该等于子节点第一个键 ${expectedKey}`);
            }
          }
        });
      });
    }

    console.log('✅ 删除操作后父节点索引键更新测试通过');

    // 测试 4.6: 递归更新失效问题专项测试
    console.log('📊 测试 4.6: 递归更新失效问题专项测试');

    // 使用问题报告中的特定测试数据
    const recursiveTree = new BPlusTree(3);
    const recursiveTestKeys = [10, 20, 5, 15, 25, 3, 7];

    recursiveTestKeys.forEach(key => {
      const result = recursiveTree.insertPure(key);
      assert(result.success === true, `插入键 ${key} 应该成功`);
    });

    // 删除键 15，这是问题报告中的关键测试用例
    const recursiveDeleteResult = recursiveTree.deletePure(15);
    assert(recursiveDeleteResult.success === true, '删除键 15 应该成功');

    // 验证键 15 完全从所有节点中移除
    const allNodesAfterDelete = recursiveTree.getAllNodes();
    allNodesAfterDelete.forEach(node => {
      assert(!node.keys.includes(15), `节点 ${node.id} 不应该包含已删除的键 15`);
    });

    // 验证所有索引键的正确性
    const internalNodesAfterDelete = allNodesAfterDelete.filter(n => !n.isLeaf);
    internalNodesAfterDelete.forEach(internalNode => {
      const internal = internalNode as import('./bPlusTree').InternalNode;
      internal.children.forEach((childId, index) => {
        if (childId && index > 0) {
          const child = allNodesAfterDelete.find(n => n.id === childId);
          if (child && child.keys.length > 0) {
            const expectedKey = child.keys[0];
            const actualKey = internal.keys[index - 1];
            assert(actualKey === expectedKey,
              `递归更新后内部节点 ${internal.id} 索引键 ${actualKey} 应该等于子节点第一个键 ${expectedKey}`);
          }
        }
      });
    });

    // 测试复杂场景的递归更新
    const complexRecursiveTree = new BPlusTree(3);
    const complexRecursiveKeys = [50, 100, 25, 75, 125, 12, 37, 62, 87, 112, 137, 6, 18, 31, 43, 56, 68, 81, 93, 106, 118, 131, 143];

    complexRecursiveKeys.forEach(key => {
      const result = complexRecursiveTree.insertPure(key);
      assert(result.success === true, `插入复杂键 ${key} 应该成功`);
    });

    // 删除一个在多层级中可能出现的键
    const complexDeleteResult = complexRecursiveTree.deletePure(25);
    assert(complexDeleteResult.success === true, '删除复杂键 25 应该成功');

    // 验证复杂场景下的索引键正确性
    const complexNodesAfterDelete = complexRecursiveTree.getAllNodes();
    complexNodesAfterDelete.forEach(node => {
      assert(!node.keys.includes(25), `复杂树节点 ${node.id} 不应该包含已删除的键 25`);
    });

    const complexInternalNodes = complexNodesAfterDelete.filter(n => !n.isLeaf);
    complexInternalNodes.forEach(internalNode => {
      const internal = internalNode as import('./bPlusTree').InternalNode;
      internal.children.forEach((childId, index) => {
        if (childId && index > 0) {
          const child = complexNodesAfterDelete.find(n => n.id === childId);
          if (child && child.keys.length > 0) {
            const expectedKey = child.keys[0];
            const actualKey = internal.keys[index - 1];
            assert(actualKey === expectedKey,
              `复杂递归更新后内部节点 ${internal.id} 索引键 ${actualKey} 应该等于子节点第一个键 ${expectedKey}`);
          }
        }
      });
    });

    console.log('✅ 递归更新失效问题专项测试通过');

    // 测试 5: 可视化状态输出
    console.log('📊 测试 5: 可视化状态输出');
    const visualState = tree.getTreeStateForVisualization();
    assert(visualState.nodes.length > 0, '应该有节点');
    assert(Array.isArray(visualState.edges), '应该有边数组');

    // 验证节点数据结构
    visualState.nodes.forEach(node => {
      assert(typeof node.id === 'string', '节点应该有 ID');
      assert(typeof node.type === 'string', '节点应该有类型');
      assert(Array.isArray(node.data.keys), '节点应该有键数组');
      assert(typeof node.data.isLeaf === 'boolean', '节点应该有 isLeaf 属性');
      assert(typeof node.data.level === 'number', '节点应该有 level 属性');
    });
    console.log('✅ 可视化状态输出测试通过');

    // 测试 6: 布局算法
    console.log('📊 测试 6: 布局算法');
    const layoutedState = tree.getLayoutedTreeState('TB');
    assert(layoutedState.nodes.length === visualState.nodes.length, '布局后节点数量应该相同');
    
    // 验证所有节点都有位置信息
    layoutedState.nodes.forEach(node => {
      assert(typeof node.position.x === 'number', '节点应该有 x 坐标');
      assert(typeof node.position.y === 'number', '节点应该有 y 坐标');
    });

    // 测试不同布局方向
    const lrLayoutedState = tree.getLayoutedTreeState('LR');
    assert(lrLayoutedState.nodes.length === layoutedState.nodes.length, 'LR 布局节点数量应该相同');
    console.log('✅ 布局算法测试通过');

    // 测试 7: 向后兼容性
    console.log('📊 测试 7: 向后兼容性');
    const allNodes = tree.getAllNodes();
    assert(allNodes.length > 0, '应该能获取所有节点');
    
    // 验证节点结构
    allNodes.forEach(node => {
      assert(typeof node.id === 'string', '节点应该有 ID');
      assert(Array.isArray(node.keys), '节点应该有键数组');
      assert(typeof node.isLeaf === 'boolean', '节点应该有 isLeaf 属性');
      assert(typeof node.level === 'number', '节点应该有 level 属性');
      
      if (node.isLeaf) {
        assert('values' in node, '叶子节点应该有 values 属性');
        assert('nextLeaf' in node, '叶子节点应该有 nextLeaf 属性');
      } else {
        assert('children' in node, '内部节点应该有 children 属性');
      }
    });
    console.log('✅ 向后兼容性测试通过');

    // 测试 8: 错误处理
    console.log('📊 测试 8: 错误处理');
    try {
      new BPlusTree(2);
      assert(false, '应该抛出错误');
    } catch (error) {
      assert(error instanceof Error, '应该抛出 Error 实例');
      assert((error as Error).message === 'B+树的阶数必须至少为3', '应该有正确的错误信息');
    }
    console.log('✅ 错误处理测试通过');

    console.log('🎉 所有测试通过！B+ 树重构成功！');
    
    // 输出性能统计
    console.log('\n📈 性能统计:');
    console.log(`- 节点总数: ${allNodes.length}`);
    console.log(`- 叶子节点数: ${allNodes.filter(n => n.isLeaf).length}`);
    console.log(`- 内部节点数: ${allNodes.filter(n => !n.isLeaf).length}`);
    console.log(`- 树的高度: ${Math.max(...allNodes.map(n => n.level)) + 1}`);
    console.log(`- 键的总数: ${tree.getAllKeys().length}`);

  } catch (error) {
    console.error('❌ 测试失败:', error instanceof Error ? error.message : error);
    throw error;
  }
}

// 如果直接运行此文件，执行验证
if (typeof window === 'undefined' && require.main === module) {
  validateBPlusTreeRefactoring();
}
