// B+树动画步骤类型定义
export type AnimationStep =
  | { type: 'traverse', nodeId: string, path: string[] }
  | { type: 'insert_key', nodeId: string, key: number }
  | { type: 'split', originalNodeId: string, newNodeId: string, promotedKey: number }
  | { type: 'delete_key', nodeId: string, key: number }
  | { type: 'merge', nodeId1: string, nodeId2: string, resultNodeId: string }
  | { type: 'redistribute', fromNodeId: string, toNodeId: string, key: number }
  | { type: 'update_parent', nodeId: string, newKey: number };

// B+树节点通用属性接口
export interface BPlusTreeNodeCommon {
  id: string;
  keys: number[];
  level: number;
  parent: string | null;
}

// 节点状态标记接口（用于内部算法状态跟踪）
interface NodeStateFlags {
  _firstKeyChanged?: boolean;
  _originalFirstKey?: number | null;
}

// 叶子节点接口
export interface LeafNode extends BPlusTreeNodeCommon, NodeStateFlags {
  isLeaf: true;
  values: number[]; // 叶子节点存储实际值
  nextLeaf: string | null; // 指向下一个叶子节点的指针
}

// 内部节点接口
export interface InternalNode extends BPlusTreeNodeCommon, NodeStateFlags {
  isLeaf: false;
  children: (string | null)[]; // 指向子节点的指针数组
}

// B+树节点联合类型
export type BPlusTreeNode = LeafNode | InternalNode;

// 纯函数式插入结果接口
export interface InsertResult {
  success: boolean;
  promotedKey: number | null;
  newNode: BPlusTreeNode | null;
  error?: string;
}

// 纯函数式删除结果接口
export interface DeleteResult {
  success: boolean;
  error?: string;
}

// React Flow 节点数据接口
export interface BPlusTreeVisualizationNodeData {
  keys: (number | null)[];
  values?: (number | null)[]; // 叶子节点的值
  children?: (string | null)[]; // 内部节点的子节点指针
  isLeaf: boolean;
  level: number;
  order: number;
  nextLeaf?: string | null; // 叶子节点的下一个叶子指针
}

// React Flow 可视化状态接口
export interface TreeVisualizationState {
  nodes: Array<{
    id: string;
    type: string;
    position: { x: number; y: number };
    data: BPlusTreeVisualizationNodeData;
  }>;
  edges: Array<{
    id: string;
    source: string;
    target: string;
    sourceHandle?: string;
    targetHandle?: string;
    type?: string;
    style?: Record<string, string | number>;
  }>;
}

// 向后兼容的旧接口（将逐步废弃）
export interface LegacyBPlusTreeNode {
  id: string;
  keys: number[];
  pointers: (string | null)[];
  isLeaf: boolean;
  level: number;
  parent?: string | null;
  next?: string | null; // 叶子节点的兄弟指针
}

// B+树类
export class BPlusTree {
  private order: number;
  private root: BPlusTreeNode | null = null;
  private nodeCounter = 0;
  private allNodes: Map<string, BPlusTreeNode> = new Map();

  // 标准化的B+树参数
  private readonly maxKeys: number;
  private readonly minKeys: number;
  private readonly splitIndex: number;

  constructor(order: number = 3) {
    if (order < 3) {
      throw new Error('B+树的阶数必须至少为3');
    }

    this.order = order;

    // 标准化计算公式
    this.maxKeys = order - 1;
    this.minKeys = Math.floor((order + 1) / 2) - 1;
    this.splitIndex = Math.floor(order / 2);
  }

  // 获取B+树参数的公共方法
  public getOrder(): number {
    return this.order;
  }

  public getMaxKeys(): number {
    return this.maxKeys;
  }

  public getMinKeys(): number {
    return this.minKeys;
  }

  public getSplitIndex(): number {
    return this.splitIndex;
  }

  // 纯函数式插入方法（不包含动画逻辑）
  public insertPure(key: number): InsertResult {
    try {
      // 如果树为空，创建根节点
      if (!this.root) {
        this.root = this.createNode(true, 0);
        const leafRoot = this.root as LeafNode;
        leafRoot.keys.push(key);
        leafRoot.values.push(key);
        return { success: true, promotedKey: null, newNode: null };
      }

      // 查找叶子节点
      const leafNode = this.findLeafNodePure(key);

      // 检查键是否已存在
      if (leafNode.keys.includes(key)) {
        return { success: false, promotedKey: null, newNode: null, error: `键 ${key} 已存在` };
      }

      // 插入键值
      this.insertKeyIntoLeafNode(leafNode, key);

      // 检查并修复节点结构
      const repairResult = this.repairAfterInsert(leafNode);
      return { success: true, ...repairResult };
    } catch (error) {
      return {
        success: false,
        promotedKey: null,
        newNode: null,
        error: error instanceof Error ? error.message : '插入失败'
      };
    }
  }

  // 纯函数式查找叶子节点（不包含动画逻辑）
  private findLeafNodePure(key: number): LeafNode {
    if (!this.root) {
      throw new Error('树为空');
    }

    let current = this.root;

    while (!current.isLeaf) {
      // 找到合适的子节点
      let childIndex = 0;
      while (childIndex < current.keys.length && key >= current.keys[childIndex]) {
        childIndex++;
      }

      const internalNode = current as InternalNode;
      const childId = internalNode.children[childIndex];
      if (!childId || !this.allNodes.has(childId)) {
        throw new Error('无效的子节点指针');
      }

      current = this.allNodes.get(childId)!;
    }

    return current as LeafNode;
  }

  // 插入后的修复方法（返回提升的键和新节点）
  private repairAfterInsert(node: BPlusTreeNode): { promotedKey: number | null; newNode: BPlusTreeNode | null } {
    // 如果节点键数超过最大值，执行分裂
    if (node.keys.length > this.maxKeys) {
      const splitResult = this.performSplit(node);

      // 处理父节点
      if (!node.parent) {
        // 根节点分裂，创建新根
        const newRoot = this.createNode(false, node.level + 1) as InternalNode;
        newRoot.keys.push(splitResult.promotedKey);
        newRoot.children.push(node.id, splitResult.newNode.id);
        node.parent = newRoot.id;
        splitResult.newNode.parent = newRoot.id;
        this.root = newRoot;
        return { promotedKey: null, newNode: null };
      } else {
        // 向父节点插入提升的键
        const parent = this.allNodes.get(node.parent)! as InternalNode;
        this.insertKeyIntoInternalNode(parent, splitResult.promotedKey);

        // 找到插入位置并插入指针
        let insertIndex = 0;
        while (insertIndex < parent.keys.length - 1 && splitResult.promotedKey > parent.keys[insertIndex]) {
          insertIndex++;
        }
        parent.children.splice(insertIndex + 1, 0, splitResult.newNode.id);
        splitResult.newNode.parent = parent.id;

        // 递归修复父节点
        return this.repairAfterInsert(parent);
      }
    }

    return { promotedKey: null, newNode: null };
  }

  // 纯函数式删除方法（不包含动画逻辑）
  public deletePure(key: number): DeleteResult {
    try {
      if (!this.root) {
        return { success: false, error: '树为空' };
      }

      // 查找叶子节点
      const leafNode = this.findLeafNodePure(key);

      // 检查键是否存在
      const keyIndex = leafNode.keys.indexOf(key);
      if (keyIndex === -1) {
        return { success: false, error: `键 ${key} 不存在` };
      }

      // 删除键值
      leafNode.keys.splice(keyIndex, 1);
      leafNode.values.splice(keyIndex, 1);

      // 检查并修复节点结构
      this.repairAfterDelete(leafNode);

      // 删除操作完成后，全局清理所有残留的目标键并修复索引键
      this.globalCleanupAndFixIndexKeys(key);

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除失败'
      };
    }
  }

  // 删除第一个键后更新父节点索引键
  private updateParentIndexKeyAfterDelete(node: BPlusTreeNode, oldKey: number, newKey: number): void {
    if (!node.parent) return;

    const parent = this.allNodes.get(node.parent);
    if (!parent || parent.isLeaf) return;

    const internalParent = parent as InternalNode;
    const nodeIndex = internalParent.children.indexOf(node.id);

    // 在B+树中，第i个子节点对应第i-1个键（第0个子节点没有对应的键）
    if (nodeIndex > 0 && nodeIndex - 1 < internalParent.keys.length) {
      const keyIndex = nodeIndex - 1;
      if (internalParent.keys[keyIndex] === oldKey) {
        internalParent.keys[keyIndex] = newKey;

        // 递归更新祖先节点
        this.updateParentIndexKeyAfterDelete(parent, oldKey, newKey);
      }
    }

    // 同时检查当前节点是否需要更新其父节点的索引键
    if (node.keys.length > 0) {
      const currentFirstKey = node.keys[0];
      if (nodeIndex > 0 && nodeIndex - 1 < internalParent.keys.length) {
        const keyIndex = nodeIndex - 1;
        if (internalParent.keys[keyIndex] !== currentFirstKey) {
          const oldParentKey = internalParent.keys[keyIndex];
          internalParent.keys[keyIndex] = currentFirstKey;
          this.updateParentIndexKeyAfterDelete(parent, oldParentKey, currentFirstKey);
        }
      }
    }
  }

  // 删除后的修复方法
  private repairAfterDelete(node: BPlusTreeNode): void {
    // 根节点特殊处理
    if (node === this.root) {
      // 如果根节点是叶子节点且为空，清空树
      if (node.isLeaf && node.keys.length === 0) {
        this.root = null;
        this.allNodes.clear();
        this.nodeCounter = 0;
        return;
      }
      // 如果根节点是内部节点且只有一个子节点，提升子节点为新根
      if (!node.isLeaf) {
        const internalRoot = node as InternalNode;
        if (internalRoot.keys.length === 0 && internalRoot.children.length === 1) {
          const newRootId = internalRoot.children[0];
          if (newRootId && this.allNodes.has(newRootId)) {
            const newRoot = this.allNodes.get(newRootId)!;
            newRoot.parent = null;
            this.allNodes.delete(node.id);
            this.root = newRoot;

            // 根节点变化后，需要重新验证整个树的索引键
            this.validateAndFixAllIndexKeys();
          }
        }
      }
      return;
    }

    // 非根节点处理
    if (node.keys.length < this.minKeys) {
      if (this.canStealFromSibling(node)) {
        this.stealFromSibling(node);
        // 借位后可能需要更新父节点索引键
        this.updateParentIndexAfterRepair(node);
      } else {
        this.mergeWithSiblingPure(node);
        // 合并操作已经在相应方法中处理了父节点键的删除
      }
    } else {
      // 即使节点键数满足要求，也可能需要更新父节点索引键
      this.updateParentIndexAfterRepair(node);
    }
  }

  // 修复后更新父节点索引键
  private updateParentIndexAfterRepair(node: BPlusTreeNode): void {
    if (!node.parent || node.keys.length === 0) return;

    const parent = this.allNodes.get(node.parent);
    if (!parent || parent.isLeaf) return;

    const internalParent = parent as InternalNode;
    const nodeIndex = internalParent.children.indexOf(node.id);

    // 在B+树中，第i个子节点对应第i-1个键（第0个子节点没有对应的键）
    if (nodeIndex > 0 && nodeIndex - 1 < internalParent.keys.length) {
      const keyIndex = nodeIndex - 1;
      const currentFirstKey = node.keys[0];

      // 如果父节点的索引键与当前节点的第一个键不匹配，需要更新
      if (internalParent.keys[keyIndex] !== currentFirstKey) {
        const oldKey = internalParent.keys[keyIndex];
        internalParent.keys[keyIndex] = currentFirstKey;

        // 递归更新祖先节点
        this.updateParentIndexKeyAfterDelete(parent, oldKey, currentFirstKey);
      }
    }

    // 递归检查父节点是否也需要更新
    this.updateParentIndexAfterRepair(parent);
  }

  // 验证并修复整个树的索引键
  private validateAndFixAllIndexKeys(): void {
    if (!this.root) return;

    const allNodes = this.getAllNodes();
    const internalNodes = allNodes.filter(n => !n.isLeaf) as InternalNode[];

    // 对每个内部节点验证和修复索引键
    internalNodes.forEach(internalNode => {
      internalNode.children.forEach((childId, index) => {
        if (childId && index > 0) {
          const child = allNodes.find(n => n.id === childId);
          if (child && child.keys.length > 0) {
            const expectedKey = child.keys[0];
            const keyIndex = index - 1;

            if (keyIndex < internalNode.keys.length) {
              const currentKey = internalNode.keys[keyIndex];
              if (currentKey !== expectedKey) {
                // 修复索引键
                internalNode.keys[keyIndex] = expectedKey;
              }
            }
          }
        }
      });
    });
  }

  // 全局清理和修复索引键（简化版本）
  private globalCleanupAndFixIndexKeys(deletedKey: number): void {
    if (!this.root) return;

    // 重建所有内部节点的children结构和索引键
    this.rebuildAllInternalStructure();

    // 验证并修复树的完整性
    this.validateAndRepairTreeStructure();
  }

  // 重建所有内部节点的结构（children和索引键）
  private rebuildAllInternalStructure(): void {
    if (!this.root) return;

    const allNodes = this.getAllNodes();
    const internalNodes = allNodes.filter(n => !n.isLeaf) as InternalNode[];

    // 对每个内部节点重建children和索引键
    internalNodes.forEach(internalNode => {
      // 重建children：只保留parent指向自己的子节点
      const children = allNodes
        .filter(n => n.parent === internalNode.id)
        .sort((a, b) => (a.keys[0] ?? Infinity) - (b.keys[0] ?? Infinity))
        .map(n => n.id);

      internalNode.children = children;

      // 重建索引键：分割键数量 = children.length - 1
      internalNode.keys = [];
      for (let i = 1; i < children.length; i++) {
        const childId = children[i];
        const child = allNodes.find(n => n.id === childId);
        if (child && child.keys.length > 0) {
          internalNode.keys.push(child.keys[0]);
        }
      }
    });
  }

  // 验证并修复树的结构完整性
  private validateAndRepairTreeStructure(): void {
    if (!this.root) return;

    const allNodes = this.getAllNodes();

    // 修复悬空的父指针
    allNodes.forEach(node => {
      if (node.parent && !allNodes.find(n => n.id === node.parent)) {
        console.warn(`修复悬空父指针: 节点 ${node.id} 的父节点 ${node.parent} 不存在`);
        node.parent = null;
      }
    });

    // 修复不一致的父子关系
    const internalNodes = allNodes.filter(n => !n.isLeaf) as InternalNode[];
    internalNodes.forEach(internalNode => {
      internalNode.children.forEach(childId => {
        if (childId) {
          const child = allNodes.find(n => n.id === childId);
          if (child && child.parent !== internalNode.id) {
            console.warn(`修复父子关系: 节点 ${childId} 的父指针不正确`);
            child.parent = internalNode.id;
          }
        }
      });
    });

    // 重新验证所有索引键
    this.validateAndFixAllIndexKeys();
  }

  // 检查是否可以从兄弟节点借位
  private canStealFromSibling(node: BPlusTreeNode): boolean {
    if (!node.parent) return false;

    const parent = this.allNodes.get(node.parent)! as InternalNode;
    const nodeIndex = parent.children.indexOf(node.id);

    // 检查右兄弟
    const rightSiblingId = parent.children[nodeIndex + 1];
    if (rightSiblingId) {
      const rightSibling = this.allNodes.get(rightSiblingId)!;
      if (rightSibling.keys.length > this.minKeys) {
        return true;
      }
    }

    // 检查左兄弟
    const leftSiblingId = parent.children[nodeIndex - 1];
    if (leftSiblingId) {
      const leftSibling = this.allNodes.get(leftSiblingId)!;
      if (leftSibling.keys.length > this.minKeys) {
        return true;
      }
    }

    return false;
  }

  // 从兄弟节点借位
  private stealFromSibling(node: BPlusTreeNode): void {
    if (!node.parent) return;

    const parent = this.allNodes.get(node.parent)! as InternalNode;
    const nodeIndex = parent.children.indexOf(node.id);

    // 优先从右兄弟借位
    const rightSiblingId = parent.children[nodeIndex + 1];
    if (rightSiblingId) {
      const rightSibling = this.allNodes.get(rightSiblingId)!;
      if (rightSibling.keys.length > this.minKeys) {
        this.stealFromRightSibling(node, rightSibling, parent, nodeIndex);
        return;
      }
    }

    // 从左兄弟借位
    const leftSiblingId = parent.children[nodeIndex - 1];
    if (leftSiblingId) {
      const leftSibling = this.allNodes.get(leftSiblingId)!;
      if (leftSibling.keys.length > this.minKeys) {
        this.stealFromLeftSibling(node, leftSibling, parent, nodeIndex);
        return;
      }
    }
  }

  // 与兄弟节点合并（纯函数版本）
  private mergeWithSiblingPure(node: BPlusTreeNode): void {
    if (!node.parent) return;

    const parent = this.allNodes.get(node.parent)! as InternalNode;
    const nodeIndex = parent.children.indexOf(node.id);

    // 优先与右兄弟合并
    const rightSiblingId = parent.children[nodeIndex + 1];
    if (rightSiblingId) {
      const rightSibling = this.allNodes.get(rightSiblingId)!;
      this.mergeWithRightSiblingPure(node, rightSibling, parent, nodeIndex);
      // 递归检查父节点
      this.repairAfterDelete(parent);
      return;
    }

    // 与左兄弟合并
    const leftSiblingId = parent.children[nodeIndex - 1];
    if (leftSiblingId) {
      const leftSibling = this.allNodes.get(leftSiblingId)!;
      this.mergeWithLeftSiblingPure(node, leftSibling, parent, nodeIndex);
      // 递归检查父节点
      this.repairAfterDelete(parent);
      return;
    }
  }

  // 从右兄弟借位（纯函数版本）
  private stealFromRightSibling(node: BPlusTreeNode, rightSibling: BPlusTreeNode, parent: InternalNode, nodeIndex: number): void {
    const keyToMove = rightSibling.keys.shift()!;

    if (node.isLeaf) {
      const leafNode = node as LeafNode;
      const rightLeafSibling = rightSibling as LeafNode;

      leafNode.keys.push(keyToMove);
      leafNode.values.push(rightLeafSibling.values.shift()!);

      // 更新父节点中指向右兄弟的索引键
      if (rightSibling.keys.length > 0) {
        parent.keys[nodeIndex] = rightSibling.keys[0];
      }
    } else {
      const internalNode = node as InternalNode;
      const rightInternalSibling = rightSibling as InternalNode;

      const childToMove = rightInternalSibling.children.shift()!;
      internalNode.keys.push(parent.keys[nodeIndex]);
      parent.keys[nodeIndex] = keyToMove;
      internalNode.children.push(childToMove);
      const child = this.allNodes.get(childToMove);
      if (child) child.parent = internalNode.id;
    }

    // 递归更新祖先节点的索引键（如果需要）
    if (rightSibling.keys.length > 0) {
      this.updateParentIndexAfterRepair(rightSibling);
    }
  }

  // 从左兄弟借位（纯函数版本）
  private stealFromLeftSibling(node: BPlusTreeNode, leftSibling: BPlusTreeNode, parent: InternalNode, nodeIndex: number): void {
    const keyToMove = leftSibling.keys.pop()!;

    if (node.isLeaf) {
      const leafNode = node as LeafNode;
      const leftLeafSibling = leftSibling as LeafNode;

      leafNode.keys.unshift(keyToMove);
      leafNode.values.unshift(leftLeafSibling.values.pop()!);

      // 更新父节点中指向当前节点的索引键
      parent.keys[nodeIndex - 1] = leafNode.keys[0];
    } else {
      const internalNode = node as InternalNode;
      const leftInternalSibling = leftSibling as InternalNode;

      internalNode.keys.unshift(parent.keys[nodeIndex - 1]);
      parent.keys[nodeIndex - 1] = keyToMove;
      const childToMove = leftInternalSibling.children.pop()!;
      internalNode.children.unshift(childToMove);
      const child = this.allNodes.get(childToMove);
      if (child) child.parent = internalNode.id;
    }

    // 递归更新祖先节点的索引键（如果需要）
    this.updateParentIndexAfterRepair(node);
    if (leftSibling.keys.length > 0) {
      this.updateParentIndexAfterRepair(leftSibling);
    }
  }

  // 与右兄弟合并（纯函数版本）
  private mergeWithRightSiblingPure(node: BPlusTreeNode, rightSibling: BPlusTreeNode, parent: InternalNode, nodeIndex: number): void {
    if (node.isLeaf) {
      const leafNode = node as LeafNode;
      const rightLeafSibling = rightSibling as LeafNode;

      leafNode.keys.push(...rightLeafSibling.keys);
      leafNode.values.push(...rightLeafSibling.values);
      leafNode.nextLeaf = rightLeafSibling.nextLeaf;
    } else {
      const internalNode = node as InternalNode;
      const rightInternalSibling = rightSibling as InternalNode;

      internalNode.keys.push(parent.keys[nodeIndex]);
      internalNode.keys.push(...rightInternalSibling.keys);
      internalNode.children.push(...rightInternalSibling.children);

      // 更新子节点的父指针
      rightInternalSibling.children.forEach(childId => {
        if (childId) {
          const child = this.allNodes.get(childId);
          if (child) child.parent = internalNode.id;
        }
      });
    }

    parent.keys.splice(nodeIndex, 1);
    parent.children.splice(nodeIndex + 1, 1);
    this.allNodes.delete(rightSibling.id);

    // 合并后可能需要更新父节点的索引键
    this.updateParentIndexAfterRepair(node);
  }

  // 与左兄弟合并（纯函数版本）
  private mergeWithLeftSiblingPure(node: BPlusTreeNode, leftSibling: BPlusTreeNode, parent: InternalNode, nodeIndex: number): void {
    if (node.isLeaf) {
      const leafNode = node as LeafNode;
      const leftLeafSibling = leftSibling as LeafNode;

      leftLeafSibling.keys.push(...leafNode.keys);
      leftLeafSibling.values.push(...leafNode.values);
      leftLeafSibling.nextLeaf = leafNode.nextLeaf;
    } else {
      const internalNode = node as InternalNode;
      const leftInternalSibling = leftSibling as InternalNode;

      leftInternalSibling.keys.push(parent.keys[nodeIndex - 1]);
      leftInternalSibling.keys.push(...internalNode.keys);
      leftInternalSibling.children.push(...internalNode.children);

      // 更新子节点的父指针
      internalNode.children.forEach(childId => {
        if (childId) {
          const child = this.allNodes.get(childId);
          if (child) child.parent = leftInternalSibling.id;
        }
      });
    }

    parent.keys.splice(nodeIndex - 1, 1);
    parent.children.splice(nodeIndex, 1);
    this.allNodes.delete(node.id);

    // 合并后可能需要更新父节点的索引键
    this.updateParentIndexAfterRepair(leftSibling);
  }

  // 获取树的可视化状态（用于 React Flow）
  public getTreeStateForVisualization(): TreeVisualizationState {
    const nodes: TreeVisualizationState['nodes'] = [];
    const edges: TreeVisualizationState['edges'] = [];

    if (!this.root) {
      return { nodes, edges };
    }

    const allNodes = this.getAllNodes();

    // 创建节点
    allNodes.forEach(node => {
      let nodeData: BPlusTreeVisualizationNodeData;

      if (node.isLeaf) {
        const leafNode = node as LeafNode;
        nodeData = {
          keys: [...leafNode.keys, ...Array(this.maxKeys - leafNode.keys.length).fill(null)],
          values: [...leafNode.values, ...Array(this.maxKeys - leafNode.values.length).fill(null)],
          isLeaf: true,
          level: leafNode.level,
          order: this.order,
          nextLeaf: leafNode.nextLeaf
        };
      } else {
        const internalNode = node as InternalNode;
        nodeData = {
          keys: [...internalNode.keys, ...Array(this.maxKeys - internalNode.keys.length).fill(null)],
          children: [...internalNode.children, ...Array(this.order - internalNode.children.length).fill(null)],
          isLeaf: false,
          level: internalNode.level,
          order: this.order
        };
      }

      nodes.push({
        id: node.id,
        type: node.isLeaf ? 'bPlusLeafNode' : 'bPlusInternalNode',
        position: { x: 0, y: 0 }, // 初始位置，将由布局算法设置
        data: nodeData
      });
    });

    // 创建边
    allNodes.forEach(node => {
      if (!node.isLeaf) {
        const internalNode = node as InternalNode;
        internalNode.children.forEach((childId, index) => {
          if (childId) {
            edges.push({
              id: `${node.id}-${childId}`,
              source: node.id,
              target: childId,
              sourceHandle: `pointer-${index}`,
              targetHandle: 'top',
              type: 'straight'
            });
          }
        });
      } else {
        // 叶子节点的兄弟指针
        const leafNode = node as LeafNode;
        if (leafNode.nextLeaf) {
          edges.push({
            id: `${node.id}-next-${leafNode.nextLeaf}`,
            source: node.id,
            target: leafNode.nextLeaf,
            sourceHandle: 'sibling',
            targetHandle: 'sibling-target',
            type: 'straight',
            style: { stroke: '#999', strokeDasharray: '5,5' }
          });
        }
      }
    });

    return { nodes, edges };
  }

  // 获取带布局的树可视化状态
  public getLayoutedTreeState(direction: 'TB' | 'LR' = 'TB'): TreeVisualizationState {
    const { nodes, edges } = this.getTreeStateForVisualization();

    if (nodes.length === 0) {
      return { nodes, edges };
    }

    return this.applyDagreLayout(nodes, edges, direction);
  }

  // 使用 Dagre 布局算法
  private applyDagreLayout(
    nodes: TreeVisualizationState['nodes'],
    edges: TreeVisualizationState['edges'],
    direction: 'TB' | 'LR' = 'TB'
  ): TreeVisualizationState {
    // 动态导入 dagre 以避免 SSR 问题
    try {
      const dagre = require('dagre');
      const dagreGraph = new dagre.graphlib.Graph();

      dagreGraph.setDefaultEdgeLabel(() => ({}));
      dagreGraph.setGraph({
        rankdir: direction,
        nodesep: 100,
        ranksep: 120,
        marginx: 50,
        marginy: 50
      });

      // 添加节点到 dagre 图
      nodes.forEach(node => {
        dagreGraph.setNode(node.id, {
          width: 200, // 节点宽度
          height: 60   // 节点高度
        });
      });

      // 添加边到 dagre 图
      edges.forEach(edge => {
        dagreGraph.setEdge(edge.source, edge.target);
      });

      // 计算布局
      dagre.layout(dagreGraph);

      // 应用布局位置到节点
      const layoutedNodes = nodes.map(node => {
        const nodeWithPosition = dagreGraph.node(node.id);
        return {
          ...node,
          position: {
            x: nodeWithPosition.x - nodeWithPosition.width / 2,
            y: nodeWithPosition.y - nodeWithPosition.height / 2
          }
        };
      });

      return { nodes: layoutedNodes, edges };
    } catch (error) {
      console.warn('Dagre layout failed, using fallback layout:', error);
      return this.applyFallbackLayout(nodes, edges);
    }
  }

  // 回退布局算法（基于层级的简单布局）
  private applyFallbackLayout(
    nodes: TreeVisualizationState['nodes'],
    edges: TreeVisualizationState['edges']
  ): TreeVisualizationState {
    const levelGroups: { [level: number]: TreeVisualizationState['nodes'][0][] } = {};

    // 按层级分组节点
    nodes.forEach(node => {
      const level = node.data.level;
      if (!levelGroups[level]) levelGroups[level] = [];
      levelGroups[level].push(node);
    });

    const layoutedNodes: TreeVisualizationState['nodes'] = [];
    const levels = Object.keys(levelGroups).map(Number).sort((a, b) => b - a);

    levels.forEach((level, levelIndex) => {
      const nodesInLevel = levelGroups[level];

      // 按首个键排序
      nodesInLevel.sort((a, b) => {
        const firstKeyA = a.data.keys.find(k => k !== null) as number | undefined ?? Infinity;
        const firstKeyB = b.data.keys.find(k => k !== null) as number | undefined ?? Infinity;
        return firstKeyA - firstKeyB;
      });

      const logicalSlotWidth = 250; // 每个节点的逻辑宽度
      const levelWidth = nodesInLevel.length * logicalSlotWidth;
      const startX = -levelWidth / 2;

      nodesInLevel.forEach((node, index) => {
        const x = startX + index * logicalSlotWidth + logicalSlotWidth / 2;
        const y = levelIndex * 120;

        layoutedNodes.push({
          ...node,
          position: { x, y }
        });
      });
    });

    return { nodes: layoutedNodes, edges };
  }

  // 创建新节点
  private createNode(isLeaf: boolean, level: number): BPlusTreeNode {
    const nodeId = `node-${this.nodeCounter++}`;

    let node: BPlusTreeNode;
    if (isLeaf) {
      node = {
        id: nodeId,
        keys: [],
        values: [], // 叶子节点存储实际值
        level,
        parent: null,
        isLeaf: true,
        nextLeaf: null
      } as LeafNode;
    } else {
      node = {
        id: nodeId,
        keys: [],
        children: [], // 内部节点存储子节点指针
        level,
        parent: null,
        isLeaf: false
      } as InternalNode;
    }

    this.allNodes.set(node.id, node);
    return node;
  }

  // 查找叶子节点
  private *findLeafNode(key: number): Generator<AnimationStep, LeafNode, unknown> {
    if (!this.root) {
      throw new Error('树为空');
    }

    let current = this.root;
    const path: string[] = [current.id];

    while (!current.isLeaf) {
      yield { type: 'traverse', nodeId: current.id, path: [...path] };

      // 找到合适的子节点
      let childIndex = 0;
      while (childIndex < current.keys.length && key >= current.keys[childIndex]) {
        childIndex++;
      }

      const internalNode = current as InternalNode;
      const childId = internalNode.children[childIndex];
      if (!childId || !this.allNodes.has(childId)) {
        throw new Error('无效的子节点指针');
      }

      current = this.allNodes.get(childId)!;
      path.push(current.id);
    }

    yield { type: 'traverse', nodeId: current.id, path: [...path] };
    return current as LeafNode;
  }

  // 插入键值的生成器函数
  public *insert(key: number): Generator<AnimationStep, void, unknown> {
    // 如果树为空，创建根节点
    if (!this.root) {
      this.root = this.createNode(true, 0);
      this.root.keys.push(key);
      yield { type: 'insert_key', nodeId: this.root.id, key };
      return;
    }

    // 阶段1：查找阶段 - 完整执行findLeafNode生成器
    const findGenerator = this.findLeafNode(key);
    let findResult = findGenerator.next();

    // 消费所有traverse步骤
    while (!findResult.done) {
      yield findResult.value as AnimationStep; // yield traverse步骤
      findResult = findGenerator.next();
    }

    // 获取查找结果：叶子节点
    const leafNode = findResult.value as LeafNode;

    // 检查键是否已存在
    if (leafNode.keys.includes(key)) {
      throw new Error(`键 ${key} 已存在`);
    }

    // 阶段2：更新阶段 - 插入键值并统一调用结构性修复
    yield { type: 'insert_key', nodeId: leafNode.id, key };
    this.insertKeyIntoLeafNode(leafNode, key);

    // 统一调用结构性修复函数
    yield* this.checkAndFixNode(leafNode);
  }

  // 在叶子节点中插入键值（保持有序）
  private insertKeyIntoLeafNode(leafNode: LeafNode, key: number): void {
    let insertIndex = 0;
    while (insertIndex < leafNode.keys.length && leafNode.keys[insertIndex] < key) {
      insertIndex++;
    }
    leafNode.keys.splice(insertIndex, 0, key);
    leafNode.values.splice(insertIndex, 0, key); // 在B+树中，叶子节点的值通常与键相同
    // 插入后如果首键变化，递归修正祖先分割键
    if (insertIndex === 0) {
      this.updateAncestorKeysRecursive(leafNode);
    }
  }

  // 在内部节点中插入键（保持有序）
  private insertKeyIntoInternalNode(internalNode: InternalNode, key: number): void {
    let insertIndex = 0;
    while (insertIndex < internalNode.keys.length && internalNode.keys[insertIndex] < key) {
      insertIndex++;
    }
    internalNode.keys.splice(insertIndex, 0, key);
  }



  // 统一的节点结构性检查和修复函数
  private *checkAndFixNode(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    // 预检查：确保节点有效
    if (!node || !this.allNodes.has(node.id)) {
      console.warn('checkAndFixNode: 节点无效或不存在');
      return;
    }

    // 情况1：节点key数超过最大值 - 执行分裂
    if (node.keys.length > this.maxKeys) {
      yield* this.splitNodeAndPropagate(node);
      return;
    }

    // 情况2：根节点特殊处理
    if (node === this.root) {
      yield* this.handleRootNodeSpecialCases(node);
      return;
    }

    // 情况3：非根节点key数少于最小值 - 执行借位或合并
    if (node.keys.length < this.minKeys) {
      if (this.canBorrowFromSibling(node)) {
        yield* this.borrowFromSibling(node);
      } else {
        yield* this.mergeWithSibling(node);
      }
      return;
    }

    // 情况4：检查并更新父节点的分割键
    if (this.isFirstKeyChanged(node)) {
      yield* this.updateAncestorKeys(node);
    }

    // 情况5：验证节点结构完整性
    this.validateNodeStructure(node);
  }

  // 处理根节点的特殊情况
  private *handleRootNodeSpecialCases(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    // 如果根节点是叶子节点且为空，清空树
    if (node.isLeaf && node.keys.length === 0) {
      this.root = null;
      this.allNodes.clear();
      this.nodeCounter = 0;
      return;
    }

    // 如果根节点是内部节点且只有一个子节点，提升子节点为新根
    if (!node.isLeaf) {
      const internalNode = node as InternalNode;
      if (internalNode.keys.length === 0 && internalNode.children.length === 1) {
        const newRootId = internalNode.children[0];
        if (newRootId && this.allNodes.has(newRootId)) {
          const newRoot = this.allNodes.get(newRootId)!;
          newRoot.parent = null;
          this.allNodes.delete(node.id);
          this.root = newRoot;

          // 根节点变化后，重新验证整个树的索引键
          this.validateAndFixAllIndexKeys();
        }
      }
    }

    // 检查根节点的首键变化
    if (this.isFirstKeyChanged(node)) {
      yield* this.updateAncestorKeys(node);
    }
  }

  // 验证节点结构完整性
  private validateNodeStructure(node: BPlusTreeNode): void {
    // 验证键的排序
    for (let i = 1; i < node.keys.length; i++) {
      if (node.keys[i] <= node.keys[i - 1]) {
        console.warn(`节点 ${node.id} 键未正确排序，正在修复...`);
        node.keys.sort((a, b) => a - b);
        if (node.isLeaf) {
          // 对于叶子节点，同时排序values
          const leafNode = node as LeafNode;
          const keyValuePairs = node.keys.map((key, index) => ({ key, value: leafNode.values[index] }));
          keyValuePairs.sort((a, b) => a.key - b.key);
          node.keys = keyValuePairs.map(pair => pair.key);
          leafNode.values = keyValuePairs.map(pair => pair.value);
        }
        break;
      }
    }

    // 验证叶子节点的键值对数量匹配
    if (node.isLeaf) {
      const leafNode = node as LeafNode;
      if (leafNode.keys.length !== leafNode.values.length) {
        console.warn(`叶子节点 ${node.id} 键值对数量不匹配，正在修复...`);
        // 截断或填充values数组以匹配keys数组
        if (leafNode.values.length > leafNode.keys.length) {
          leafNode.values = leafNode.values.slice(0, leafNode.keys.length);
        } else {
          while (leafNode.values.length < leafNode.keys.length) {
            leafNode.values.push(leafNode.keys[leafNode.values.length]);
          }
        }
      }
    }

    // 验证内部节点的子指针数量
    if (!node.isLeaf) {
      const internalNode = node as InternalNode;
      const expectedChildCount = node.keys.length + 1;
      if (internalNode.children.length !== expectedChildCount) {
        console.warn(`内部节点 ${node.id} 子指针数量不正确，正在修复...`);
        // 调整children数组长度
        if (internalNode.children.length > expectedChildCount) {
          internalNode.children = internalNode.children.slice(0, expectedChildCount);
        } else {
          while (internalNode.children.length < expectedChildCount) {
            internalNode.children.push(null);
          }
        }
      }
    }
  }

  // 分裂节点并向上传播
  private *splitNodeAndPropagate(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    const splitResult = this.performSplit(node);
    yield {
      type: 'split',
      originalNodeId: node.id,
      newNodeId: splitResult.newNode.id,
      promotedKey: splitResult.promotedKey
    };

    // 处理父节点
    if (!node.parent) {
      // 根节点分裂，创建新根
      const newRoot = this.createNode(false, node.level + 1) as InternalNode;
      newRoot.keys.push(splitResult.promotedKey);
      newRoot.children.push(node.id, splitResult.newNode.id);
      node.parent = newRoot.id;
      splitResult.newNode.parent = newRoot.id;
      this.root = newRoot;
    } else {
      // 向父节点插入提升的键
      const parent = this.allNodes.get(node.parent)! as InternalNode;
      this.insertKeyIntoInternalNode(parent, splitResult.promotedKey);

      // 找到插入位置并插入指针
      let insertIndex = 0;
      while (insertIndex < parent.keys.length - 1 && splitResult.promotedKey > parent.keys[insertIndex]) {
        insertIndex++;
      }
      parent.children.splice(insertIndex + 1, 0, splitResult.newNode.id);
      splitResult.newNode.parent = parent.id;

      // 递归检查父节点
      yield* this.checkAndFixNode(parent);
    }
  }

  // 执行节点分裂操作
  private performSplit(node: BPlusTreeNode): { newNode: BPlusTreeNode; promotedKey: number } {
    if (node.isLeaf) {
      // 叶子节点分裂
      const leafNode = node as LeafNode;
      const newLeafNode = this.createNode(true, leafNode.level) as LeafNode;

      // 分裂键和值 - 使用标准化的分裂索引
      newLeafNode.keys = leafNode.keys.splice(this.splitIndex);
      newLeafNode.values = leafNode.values.splice(this.splitIndex);

      // 更新叶子节点链表
      newLeafNode.nextLeaf = leafNode.nextLeaf;
      leafNode.nextLeaf = newLeafNode.id;
      newLeafNode.parent = leafNode.parent;

      const promotedKey = newLeafNode.keys[0];
      return { newNode: newLeafNode, promotedKey };
    } else {
      // 内部节点分裂
      const internalNode = node as InternalNode;
      const newInternalNode = this.createNode(false, internalNode.level) as InternalNode;

      // 使用标准化的分裂索引
      const promotedKey = internalNode.keys[this.splitIndex];
      newInternalNode.keys = internalNode.keys.splice(this.splitIndex + 1);
      newInternalNode.children = internalNode.children.splice(this.splitIndex + 1);
      internalNode.keys.splice(this.splitIndex, 1); // 移除提升的键
      newInternalNode.parent = internalNode.parent;

      // 更新子节点的父指针
      newInternalNode.children.forEach(childId => {
        if (childId) {
          const child = this.allNodes.get(childId);
          if (child) child.parent = newInternalNode.id;
        }
      });

      return { newNode: newInternalNode, promotedKey };
    }
  }



  // 删除键值的生成器函数
  public *delete(key: number): Generator<AnimationStep, void, unknown> {
    if (!this.root) {
      throw new Error('树为空');
    }

    // 阶段1：查找阶段 - 完整执行findLeafNode生成器
    const findGenerator = this.findLeafNode(key);
    let findResult = findGenerator.next();

    // 消费所有traverse步骤
    while (!findResult.done) {
      yield findResult.value as AnimationStep; // yield traverse步骤
      findResult = findGenerator.next();
    }

    // 获取查找结果：叶子节点
    const leafNode = findResult.value as LeafNode;

    // 检查键是否存在
    const keyIndex = leafNode.keys.indexOf(key);
    if (keyIndex === -1) {
      throw new Error(`键 ${key} 不存在`);
    }

    // 阶段2：更新阶段 - 删除键值并统一调用结构性修复
    yield { type: 'delete_key', nodeId: leafNode.id, key };

    // 记录删除前的首键和是否删除的是首个键
    const originalFirstKey = leafNode.keys[0];
    const isFirstKeyDeleted = keyIndex === 0;
    leafNode.keys.splice(keyIndex, 1);
    leafNode.values.splice(keyIndex, 1);

    // 删除后如果首键变化，递归修正祖先分割键
    if (isFirstKeyDeleted && leafNode.keys.length > 0 && originalFirstKey !== leafNode.keys[0]) {
      this.updateAncestorKeysRecursive(leafNode);
    }

    // 统一调用结构性修复函数
    yield* this.checkAndFixNode(leafNode);

    // 动画流程最后也要彻底修复结构和索引，保证和 deletePure 一致
    this.repairAfterDelete(leafNode);
    this.globalCleanupAndFixIndexKeys(key);
  }







  // 检查是否可以从兄弟节点借位
  private canBorrowFromSibling(node: BPlusTreeNode): boolean {
    if (!node.parent) return false;

    const parent = this.allNodes.get(node.parent)! as InternalNode;
    const nodeIndex = parent.children.indexOf(node.id);

    // 检查右兄弟
    const rightSiblingId = parent.children[nodeIndex + 1];
    if (rightSiblingId) {
      const rightSibling = this.allNodes.get(rightSiblingId)!;
      if (rightSibling.keys.length > this.minKeys) {
        return true;
      }
    }

    // 检查左兄弟
    const leftSiblingId = parent.children[nodeIndex - 1];
    if (leftSiblingId) {
      const leftSibling = this.allNodes.get(leftSiblingId)!;
      if (leftSibling.keys.length > this.minKeys) {
        return true;
      }
    }

    return false;
  }

  // 从兄弟节点借位
  private *borrowFromSibling(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    if (!node.parent) return;

    const parent = this.allNodes.get(node.parent)! as InternalNode;
    const nodeIndex = parent.children.indexOf(node.id);

    // 优先从右兄弟借位
    const rightSiblingId = parent.children[nodeIndex + 1];
    if (rightSiblingId) {
      const rightSibling = this.allNodes.get(rightSiblingId)!;
      if (rightSibling.keys.length > this.minKeys) {
        yield* this.borrowFromRightSibling(node, rightSibling, parent, nodeIndex);
        return;
      }
    }

    // 从左兄弟借位
    const leftSiblingId = parent.children[nodeIndex - 1];
    if (leftSiblingId) {
      const leftSibling = this.allNodes.get(leftSiblingId)!;
      if (leftSibling.keys.length > this.minKeys) {
        yield* this.borrowFromLeftSibling(node, leftSibling, parent, nodeIndex);
        return;
      }
    }
  }

  // 与兄弟节点合并
  private *mergeWithSibling(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    if (!node.parent) return;

    const parent = this.allNodes.get(node.parent)! as InternalNode;
    const nodeIndex = parent.children.indexOf(node.id);

    // 优先与右兄弟合并
    const rightSiblingId = parent.children[nodeIndex + 1];
    if (rightSiblingId) {
      const rightSibling = this.allNodes.get(rightSiblingId)!;
      yield* this.mergeWithRightSibling(node, rightSibling, parent, nodeIndex);
      // 递归检查父节点
      yield* this.checkAndFixNode(parent);
      return;
    }

    // 与左兄弟合并
    const leftSiblingId = parent.children[nodeIndex - 1];
    if (leftSiblingId) {
      const leftSibling = this.allNodes.get(leftSiblingId)!;
      yield* this.mergeWithLeftSibling(node, leftSibling, parent, nodeIndex);
      // 递归检查父节点
      yield* this.checkAndFixNode(parent);
      return;
    }
  }

  // 从右兄弟借键
  private *borrowFromRightSibling(node: BPlusTreeNode, rightSibling: BPlusTreeNode,
    parent: InternalNode, nodeIndex: number): Generator<AnimationStep, void, unknown> {
    const keyToMove = rightSibling.keys.shift()!;
    yield { type: 'redistribute', fromNodeId: rightSibling.id, toNodeId: node.id, key: keyToMove };

    if (node.isLeaf) {
      const leafNode = node as LeafNode;
      const rightLeafSibling = rightSibling as LeafNode;

      leafNode.keys.push(keyToMove);
      leafNode.values.push(rightLeafSibling.values.shift()!);
      parent.keys[nodeIndex] = rightSibling.keys[0];
      // 借位后如果首键变化，递归修正祖先分割键
      if (leafNode.keys.length === 1 || leafNode.keys[0] === keyToMove) {
        this.updateAncestorKeysRecursive(leafNode);
      }
    } else {
      const internalNode = node as InternalNode;
      const rightInternalSibling = rightSibling as InternalNode;

      const childToMove = rightInternalSibling.children.shift()!;
      internalNode.keys.push(parent.keys[nodeIndex]);
      parent.keys[nodeIndex] = keyToMove;
      internalNode.children.push(childToMove);
      const child = this.allNodes.get(childToMove);
      if (child) child.parent = internalNode.id;
      // 借位后如果首键变化，递归修正祖先分割键
      if (internalNode.keys.length === 1 || internalNode.keys[0] === parent.keys[nodeIndex]) {
        this.updateAncestorKeysRecursive(internalNode);
      }
    }

    yield { type: 'update_parent', nodeId: parent.id, newKey: parent.keys[nodeIndex] };
  }

  // 从左兄弟借键
  private *borrowFromLeftSibling(node: BPlusTreeNode, leftSibling: BPlusTreeNode,
    parent: InternalNode, nodeIndex: number): Generator<AnimationStep, void, unknown> {
    const keyToMove = leftSibling.keys.pop()!;
    yield { type: 'redistribute', fromNodeId: leftSibling.id, toNodeId: node.id, key: keyToMove };

    // 记录原始首键
    const originalFirstKey = node.keys.length > 0 ? node.keys[0] : null;

    if (node.isLeaf) {
      const leafNode = node as LeafNode;
      const leftLeafSibling = leftSibling as LeafNode;

      leafNode.keys.unshift(keyToMove);
      leafNode.values.unshift(leftLeafSibling.values.pop()!);
      parent.keys[nodeIndex - 1] = leafNode.keys[0];
      // 借位后如果首键变化，递归修正祖先分割键
      if (originalFirstKey !== leafNode.keys[0]) {
        this.updateAncestorKeysRecursive(leafNode);
      }
    } else {
      const internalNode = node as InternalNode;
      const leftInternalSibling = leftSibling as InternalNode;

      internalNode.keys.unshift(parent.keys[nodeIndex - 1]);
      parent.keys[nodeIndex - 1] = keyToMove;
      const childToMove = leftInternalSibling.children.pop()!;
      internalNode.children.unshift(childToMove);
      const child = this.allNodes.get(childToMove);
      if (child) child.parent = internalNode.id;
      // 借位后如果首键变化，递归修正祖先分割键
      if (originalFirstKey !== internalNode.keys[0]) {
        this.updateAncestorKeysRecursive(internalNode);
      }
    }

    yield { type: 'update_parent', nodeId: parent.id, newKey: parent.keys[nodeIndex - 1] };
  }

  // 与右兄弟合并
  private *mergeWithRightSibling(node: BPlusTreeNode, rightSibling: BPlusTreeNode,
    parent: InternalNode, nodeIndex: number): Generator<AnimationStep, void, unknown> {
    yield { type: 'merge', nodeId1: node.id, nodeId2: rightSibling.id, resultNodeId: node.id };

    let oldFirstKey: number | null = null;
    if (node.keys.length > 0) oldFirstKey = node.keys[0];

    if (node.isLeaf) {
      const leafNode = node as LeafNode;
      const rightLeafSibling = rightSibling as LeafNode;

      leafNode.keys.push(...rightLeafSibling.keys);
      leafNode.values.push(...rightLeafSibling.values);
      leafNode.nextLeaf = rightLeafSibling.nextLeaf;
    } else {
      const internalNode = node as InternalNode;
      const rightInternalSibling = rightSibling as InternalNode;

      internalNode.keys.push(parent.keys[nodeIndex]);
      internalNode.keys.push(...rightInternalSibling.keys);
      internalNode.children.push(...rightInternalSibling.children);

      // 更新子节点的父指针
      rightInternalSibling.children.forEach(childId => {
        if (childId) {
          const child = this.allNodes.get(childId);
          if (child) child.parent = internalNode.id;
        }
      });
    }

    parent.keys.splice(nodeIndex, 1);
    parent.children.splice(nodeIndex + 1, 1);
    this.allNodes.delete(rightSibling.id);

    // 合并后如果首键变化，递归修正祖先分割键
    if (node.keys.length > 0 && oldFirstKey !== node.keys[0]) {
      this.updateAncestorKeysRecursive(node);
    }
  }

  // 与左兄弟合并
  private *mergeWithLeftSibling(node: BPlusTreeNode, leftSibling: BPlusTreeNode,
    parent: InternalNode, nodeIndex: number): Generator<AnimationStep, void, unknown> {
    yield { type: 'merge', nodeId1: leftSibling.id, nodeId2: node.id, resultNodeId: leftSibling.id };

    let oldFirstKey: number | null = null;
    if (leftSibling.keys.length > 0) oldFirstKey = leftSibling.keys[0];

    if (node.isLeaf) {
      const leafNode = node as LeafNode;
      const leftLeafSibling = leftSibling as LeafNode;

      leftLeafSibling.keys.push(...leafNode.keys);
      leftLeafSibling.values.push(...leafNode.values);
      leftLeafSibling.nextLeaf = leafNode.nextLeaf;
    } else {
      const internalNode = node as InternalNode;
      const leftInternalSibling = leftSibling as InternalNode;

      leftInternalSibling.keys.push(parent.keys[nodeIndex - 1]);
      leftInternalSibling.keys.push(...internalNode.keys);
      leftInternalSibling.children.push(...internalNode.children);

      // 更新子节点的父指针
      internalNode.children.forEach(childId => {
        if (childId) {
          const child = this.allNodes.get(childId);
          if (child) child.parent = leftInternalSibling.id;
        }
      });
    }

    parent.keys.splice(nodeIndex - 1, 1);
    parent.children.splice(nodeIndex, 1);
    this.allNodes.delete(node.id);

    // 合并后如果首键变化，递归修正祖先分割键
    if (leftSibling.keys.length > 0 && oldFirstKey !== leftSibling.keys[0]) {
      this.updateAncestorKeysRecursive(leftSibling);
    }
  }

  // 检查节点首个key是否发生变化
  private isFirstKeyChanged(node: BPlusTreeNode): boolean {
    // 检查是否有首键变化标记
    return node._firstKeyChanged === true;
  }

  // 递归向上更新祖先节点的分割键
  private *updateAncestorKeys(node: BPlusTreeNode): Generator<AnimationStep, void, unknown> {
    if (!node.parent || node.keys.length === 0) return;

    const newFirstKey = node.keys[0];
    const parent = this.allNodes.get(node.parent)!;

    // 获取更新前父节点的首键，用于判断是否需要继续向上传播
    const parentOriginalFirstKey = parent.keys.length > 0 ? parent.keys[0] : null;

    // 更新父节点中的索引键
    const wasUpdated = this.updateParentIndexKey(node.id, newFirstKey);

    if (wasUpdated) {
      yield { type: 'update_parent', nodeId: parent.id, newKey: newFirstKey };

      // 检查父节点的首键是否因为更新而发生了变化
      const parentNewFirstKey = parent.keys.length > 0 ? parent.keys[0] : null;
      if (parentOriginalFirstKey !== parentNewFirstKey && parentNewFirstKey !== null) {
        // 父节点首键发生变化，设置标记并继续向上递归
        parent._firstKeyChanged = true;
        parent._originalFirstKey = parentOriginalFirstKey;
        yield* this.updateAncestorKeys(parent);
      }
    }

    // 清除当前节点的首键变化标记
    delete node._firstKeyChanged;
    delete node._originalFirstKey;
  }

  // 递归向上修正所有祖先节点的分割键
  private updateAncestorKeysRecursive(node: BPlusTreeNode) {
    if (!node.parent || node.keys.length === 0) return;
    const parent = this.allNodes.get(node.parent)! as InternalNode;
    const childIndex = parent.children.indexOf(node.id);
    if (childIndex > 0 && childIndex - 1 < parent.keys.length) {
      const oldKey = parent.keys[childIndex - 1];
      const newKey = node.keys[0];
      if (oldKey !== newKey) {
        parent.keys[childIndex - 1] = newKey;
        // 递归向上
        this.updateAncestorKeysRecursive(parent);
      }
    }
  }

  // 更新父节点中指向该节点的索引键，返回是否成功更新
  private updateParentIndexKey(nodeId: string, newFirstKey: number): boolean {
    const node = this.allNodes.get(nodeId);
    if (!node || !node.parent) {
      return false;
    }

    const parent = this.allNodes.get(node.parent);
    if (!parent || parent.isLeaf) {
      return false;
    }

    const internalParent = parent as InternalNode;

    // 找到父节点中指向该节点的指针位置
    const childIndex = internalParent.children.indexOf(nodeId);
    if (childIndex === -1) {
      return false;
    }

    // 更新相应的索引键
    // 对于内部节点，第i个指针对应第i-1个键（第0个指针没有对应的键）
    if (childIndex > 0 && childIndex - 1 < internalParent.keys.length) {
      const oldKey = internalParent.keys[childIndex - 1];
      if (oldKey !== newFirstKey) {
        internalParent.keys[childIndex - 1] = newFirstKey;
        return true;
      }
    }

    return false;
  }







  // 获取所有节点（用于可视化）
  public getAllNodes(): BPlusTreeNode[] {
    return Array.from(this.allNodes.values());
  }

  // 获取根节点
  public getRoot(): BPlusTreeNode | null {
    return this.root;
  }

  // 高效查找键是否存在
  public find(key: number): boolean {
    if (!this.root) {
      return false;
    }

    // 找到最左边的叶子节点
    let current = this.root;
    while (!current.isLeaf) {
      const internalNode = current as InternalNode;
      const firstChildId = internalNode.children[0];
      if (!firstChildId || !this.allNodes.has(firstChildId)) {
        return false;
      }
      current = this.allNodes.get(firstChildId)!;
    }

    // 遍历所有叶子节点，查找指定键
    let leafNode = current as LeafNode;
    while (leafNode) {
      // 检查当前叶子节点是否包含目标键
      if (leafNode.keys.includes(key)) {
        return true;
      }

      // 移动到下一个叶子节点
      if (leafNode.nextLeaf && this.allNodes.has(leafNode.nextLeaf)) {
        leafNode = this.allNodes.get(leafNode.nextLeaf)! as LeafNode;
      } else {
        break;
      }
    }

    return false;
  }

  // 获取所有键的排序数组
  public getAllKeys(): number[] {
    const keys: number[] = [];

    if (!this.root) {
      return keys;
    }

    // 找到最左边的叶子节点
    let current = this.root;
    while (!current.isLeaf) {
      const internalNode = current as InternalNode;
      const firstChildId = internalNode.children[0];
      if (!firstChildId || !this.allNodes.has(firstChildId)) {
        break;
      }
      current = this.allNodes.get(firstChildId)!;
    }

    // 遍历所有叶子节点，收集键
    let leafNode = current as LeafNode;
    while (leafNode) {
      // 添加当前叶子节点的所有键
      leafNode.keys.forEach(key => {
        keys.push(key);
      });

      // 移动到下一个叶子节点
      if (leafNode.nextLeaf && this.allNodes.has(leafNode.nextLeaf)) {
        leafNode = this.allNodes.get(leafNode.nextLeaf)! as LeafNode;
      } else {
        break;
      }
    }

    return keys.sort((a, b) => a - b);
  }

  // 清空树
  public clear(): void {
    this.root = null;
    this.allNodes.clear();
    this.nodeCounter = 0;
  }
}
