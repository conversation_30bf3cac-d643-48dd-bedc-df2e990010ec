/**
 * B+树验证框架的类型定义
 * 提供验证结果、错误类型和相关数据结构
 */

// 验证错误类型枚举
export enum ValidationErrorType {
  // 结构性错误
  NODE_UNDERFLOW = 'NODE_UNDERFLOW',
  NODE_OVERFLOW = 'NODE_OVERFLOW',
  INVALID_PARENT_POINTER = 'INVALID_PARENT_POINTER',
  BROKEN_LEAF_CHAIN = 'BROKEN_LEAF_CHAIN',
  INVALID_CHILD_POINTER = 'INVALID_CHILD_POINTER',
  
  // 索引键错误
  INCORRECT_INDEX_KEY = 'INCORRECT_INDEX_KEY',
  DUPLICATE_INDEX_KEY = 'DUPLICATE_INDEX_KEY',
  MISSING_INDEX_KEY = 'MISSING_INDEX_KEY',
  
  // 数据完整性错误
  UNSORTED_KEYS = 'UNSORTED_KEYS',
  DUPLICATE_KEYS = 'DUPLICATE_KEYS',
  MISMATCHED_KEY_VALUE_COUNT = 'MISMATCHED_KEY_VALUE_COUNT',
  
  // 指针错误
  DANGLING_POINTER = 'DANGLING_POINTER',
  CIRCULAR_REFERENCE = 'CIRCULAR_REFERENCE',
  ORPHANED_NODE = 'ORPHANED_NODE'
}

// 验证错误严重程度
export enum ValidationSeverity {
  CRITICAL = 'CRITICAL',
  WARNING = 'WARNING',
  INFO = 'INFO'
}

// 验证错误接口
export interface ValidationError {
  type: ValidationErrorType;
  severity: ValidationSeverity;
  nodeId: string;
  message: string;
  details?: Record<string, any>;
}

// 验证警告接口
export interface ValidationWarning {
  type: string;
  nodeId: string;
  message: string;
  suggestion?: string;
}

// 验证结果接口
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  nodeId?: string;
  timestamp: number;
}

// 树级别验证状态接口
export interface TreeValidationState {
  isValid: boolean;
  structuralErrors: ValidationError[];
  indexKeyErrors: ValidationError[];
  pointerErrors: ValidationError[];
  leafChainErrors: ValidationError[];
  totalErrorCount: number;
  totalWarningCount: number;
  lastValidationTime: number;
}

// 修复操作类型枚举
export enum RepairActionType {
  REBUILD_INDEX_KEYS = 'REBUILD_INDEX_KEYS',
  FIX_PARENT_POINTERS = 'FIX_PARENT_POINTERS',
  REBUILD_LEAF_CHAIN = 'REBUILD_LEAF_CHAIN',
  REMOVE_DANGLING_POINTERS = 'REMOVE_DANGLING_POINTERS',
  MERGE_UNDERFLOW_NODES = 'MERGE_UNDERFLOW_NODES',
  SPLIT_OVERFLOW_NODES = 'SPLIT_OVERFLOW_NODES',
  SORT_NODE_KEYS = 'SORT_NODE_KEYS',
  REMOVE_DUPLICATE_KEYS = 'REMOVE_DUPLICATE_KEYS'
}

// 修复操作接口
export interface RepairAction {
  type: RepairActionType;
  nodeId: string;
  description: string;
  parameters: Record<string, any>;
  priority: number; // 修复优先级，数字越小优先级越高
}

// 修复结果接口
export interface RepairResult {
  success: boolean;
  repaired: boolean;
  changes: StructureChange[];
  additionalValidationNeeded: boolean;
  error?: string;
}

// 结构变化类型枚举
export enum StructureChangeType {
  NODE_CREATED = 'NODE_CREATED',
  NODE_DELETED = 'NODE_DELETED',
  NODE_MODIFIED = 'NODE_MODIFIED',
  KEY_ADDED = 'KEY_ADDED',
  KEY_REMOVED = 'KEY_REMOVED',
  KEY_MODIFIED = 'KEY_MODIFIED',
  POINTER_ADDED = 'POINTER_ADDED',
  POINTER_REMOVED = 'POINTER_REMOVED',
  POINTER_MODIFIED = 'POINTER_MODIFIED'
}

// 结构变化接口
export interface StructureChange {
  type: StructureChangeType;
  nodeId: string;
  description: string;
  oldValue?: any;
  newValue?: any;
  timestamp: number;
}

// 验证配置接口
export interface ValidationConfig {
  enableAutoRepair: boolean;
  validationLevel: 'BASIC' | 'COMPREHENSIVE' | 'STRICT';
  repairStrategy: 'CONSERVATIVE' | 'AGGRESSIVE';
  maxRepairAttempts: number;
  enableDetailedLogging: boolean;
}

// 默认验证配置
export const DEFAULT_VALIDATION_CONFIG: ValidationConfig = {
  enableAutoRepair: true,
  validationLevel: 'COMPREHENSIVE',
  repairStrategy: 'CONSERVATIVE',
  maxRepairAttempts: 3,
  enableDetailedLogging: false
};