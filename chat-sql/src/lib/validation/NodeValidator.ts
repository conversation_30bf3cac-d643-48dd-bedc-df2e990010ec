/**
 * NodeValidator 类
 * 提供单个节点的完整性验证功能
 */

import { BPlusTreeNode, LeafNode, InternalNode } from '../bPlusTree';
import {
  ValidationResult,
  ValidationError,
  ValidationWarning,
  ValidationErrorType,
  ValidationSeverity,
  ValidationConfig,
  DEFAULT_VALIDATION_CONFIG
} from './types';

export class NodeValidator {
  private config: ValidationConfig;
  private order: number;
  private maxKeys: number;
  private minKeys: number;

  constructor(order: number, config: ValidationConfig = DEFAULT_VALIDATION_CONFIG) {
    this.config = config;
    this.order = order;
    this.maxKeys = order - 1;
    this.minKeys = Math.floor((order + 1) / 2) - 1;
  }

  /**
   * 验证单个节点的完整性
   */
  public validateNode(node: BPlusTreeNode, allNodes?: Map<string, BPlusTreeNode>): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 基本结构验证
    this.validateBasicStructure(node, errors, warnings);

    // 键数量验证
    this.validateKeyCount(node, errors, warnings);

    // 键排序验证
    this.validateKeySorting(node, errors, warnings);

    // 节点特定验证
    if (node.isLeaf) {
      this.validateLeafNode(node as LeafNode, errors, warnings);
    } else {
      this.validateInternalNode(node as Int