/**
 * NodeValidator 类
 * 提供单个节点的完整性验证功能
 */

import { BPlusTreeNode, LeafNode, InternalNode } from '../bPlusTree';
import {
  ValidationResult,
  ValidationError,
  ValidationWarning,
  ValidationErrorType,
  ValidationSeverity,
  ValidationConfig,
  DEFAULT_VALIDATION_CONFIG
} from './types';

export class NodeValidator {
  private config: ValidationConfig;
  private order: number;
  private maxKeys: number;
  private minKeys: number;

  constructor(order: number, config: ValidationConfig = DEFAULT_VALIDATION_CONFIG) {
    this.config = config;
    this.order = order;
    this.maxKeys = order - 1;
    this.minKeys = Math.floor((order + 1) / 2) - 1;
  }

  /**
   * 验证单个节点的完整性
   */
  public validateNode(node: BPlusTreeNode, allNodes?: Map<string, BPlusTreeNode>): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 基本结构验证
    this.validateBasicStructure(node, errors, warnings);

    // 键数量验证
    this.validateKeyCount(node, errors, warnings);

    // 键排序验证
    this.validateKeySorting(node, errors, warnings);

    // 节点特定验证
    if (node.isLeaf) {
      this.validateLeafNode(node as LeafNode, errors, warnings);
    } else {
      this.validateInternalNode(node as InternalNode, errors, warnings, allNodes);
    }

    // 父子关系验证（如果提供了所有节点的映射）
    if (allNodes && node.parent) {
      this.validateParentChildRelationship(node, allNodes, errors, warnings);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      nodeId: node.id,
      timestamp: Date.now()
    };
  }

  /**
   * 验证节点基本结构
   */
  private validateBasicStructure(node: BPlusTreeNode, errors: ValidationError[], warnings: ValidationWarning[]): void {
    // 验证节点ID
    if (!node.id || typeof node.id !== 'string') {
      errors.push({
        type: ValidationErrorType.INVALID_PARENT_POINTER,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id || 'unknown',
        message: '节点ID无效或缺失'
      });
    }

    // 验证keys数组
    if (!Array.isArray(node.keys)) {
      errors.push({
        type: ValidationErrorType.UNSORTED_KEYS,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id,
        message: '节点keys属性必须是数组'
      });
    }

    // 验证level
    if (typeof node.level !== 'number' || node.level < 0) {
      errors.push({
        type: ValidationErrorType.INVALID_PARENT_POINTER,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id,
        message: '节点level属性无效'
      });
    }
  }

  /**
   * 验证节点键数量
   */
  private validateKeyCount(node: BPlusTreeNode, errors: ValidationError[], warnings: ValidationWarning[]): void {
    const keyCount = node.keys.length;

    // 检查上溢
    if (keyCount > this.maxKeys) {
      errors.push({
        type: ValidationErrorType.NODE_OVERFLOW,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id,
        message: `节点键数量(${keyCount})超过最大值(${this.maxKeys})`,
        details: { keyCount, maxKeys: this.maxKeys }
      });
    }

    // 检查下溢（根节点除外）
    if (keyCount < this.minKeys && node.parent !== null) {
      errors.push({
        type: ValidationErrorType.NODE_UNDERFLOW,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id,
        message: `非根节点键数量(${keyCount})少于最小值(${this.minKeys})`,
        details: { keyCount, minKeys: this.minKeys }
      });
    }

    // 空节点警告
    if (keyCount === 0 && node.parent !== null) {
      warnings.push({
        type: 'EMPTY_NON_ROOT_NODE',
        nodeId: node.id,
        message: '非根节点为空',
        suggestion: '考虑与兄弟节点合并或从父节点删除'
      });
    }
  }

  /**
   * 验证键的排序
   */
  private validateKeySorting(node: BPlusTreeNode, errors: ValidationError[], warnings: ValidationWarning[]): void {
    const keys = node.keys;

    // 检查重复键
    const uniqueKeys = new Set(keys);
    if (uniqueKeys.size !== keys.length) {
      errors.push({
        type: ValidationErrorType.DUPLICATE_KEYS,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id,
        message: '节点包含重复的键',
        details: { keys, duplicates: keys.filter((key, index) => keys.indexOf(key) !== index) }
      });
    }

    // 检查排序
    for (let i = 1; i < keys.length; i++) {
      if (keys[i] <= keys[i - 1]) {
        errors.push({
          type: ValidationErrorType.UNSORTED_KEYS,
          severity: ValidationSeverity.CRITICAL,
          nodeId: node.id,
          message: `键未正确排序: ${keys[i - 1]} >= ${keys[i]} at position ${i}`,
          details: { keys, errorPosition: i }
        });
        break;
      }
    }
  }

  /**
   * 验证叶子节点
   */
  private validateLeafNode(node: LeafNode, errors: ValidationError[], warnings: ValidationWarning[]): void {
    // 验证values数组
    if (!Array.isArray(node.values)) {
      errors.push({
        type: ValidationErrorType.MISMATCHED_KEY_VALUE_COUNT,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id,
        message: '叶子节点values属性必须是数组'
      });
      return;
    }

    // 验证键值对数量匹配
    if (node.keys.length !== node.values.length) {
      errors.push({
        type: ValidationErrorType.MISMATCHED_KEY_VALUE_COUNT,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id,
        message: `键数量(${node.keys.length})与值数量(${node.values.length})不匹配`,
        details: { keyCount: node.keys.length, valueCount: node.values.length }
      });
    }

    // 验证nextLeaf指针
    if (node.nextLeaf !== null && typeof node.nextLeaf !== 'string') {
      errors.push({
        type: ValidationErrorType.INVALID_CHILD_POINTER,
        severity: ValidationSeverity.WARNING,
        nodeId: node.id,
        message: 'nextLeaf指针类型无效'
      });
    }
  }

  /**
   * 验证内部节点
   */
  private validateInternalNode(
    node: InternalNode,
    errors: ValidationError[],
    warnings: ValidationWarning[],
    allNodes?: Map<string, BPlusTreeNode>
  ): void {
    // 验证children数组
    if (!Array.isArray(node.children)) {
      errors.push({
        type: ValidationErrorType.INVALID_CHILD_POINTER,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id,
        message: '内部节点children属性必须是数组'
      });
      return;
    }

    // 验证子指针数量：应该比键数量多1
    const expectedChildCount = node.keys.length + 1;
    if (node.children.length !== expectedChildCount) {
      errors.push({
        type: ValidationErrorType.INVALID_CHILD_POINTER,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id,
        message: `子指针数量(${node.children.length})应该比键数量(${node.keys.length})多1`,
        details: { childCount: node.children.length, keyCount: node.keys.length, expected: expectedChildCount }
      });
    }

    // 验证子指针有效性
    if (allNodes) {
      node.children.forEach((childId, index) => {
        if (childId === null) {
          errors.push({
            type: ValidationErrorType.DANGLING_POINTER,
            severity: ValidationSeverity.CRITICAL,
            nodeId: node.id,
            message: `子指针${index}为null`,
            details: { childIndex: index }
          });
        } else if (!allNodes.has(childId)) {
          errors.push({
            type: ValidationErrorType.DANGLING_POINTER,
            severity: ValidationSeverity.CRITICAL,
            nodeId: node.id,
            message: `子指针${index}指向不存在的节点: ${childId}`,
            details: { childIndex: index, childId }
          });
        }
      });
    }
  }

  /**
   * 验证父子关系
   */
  private validateParentChildRelationship(
    node: BPlusTreeNode,
    allNodes: Map<string, BPlusTreeNode>,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    if (!node.parent) return;

    const parent = allNodes.get(node.parent);
    if (!parent) {
      errors.push({
        type: ValidationErrorType.INVALID_PARENT_POINTER,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id,
        message: `父指针指向不存在的节点: ${node.parent}`
      });
      return;
    }

    if (parent.isLeaf) {
      errors.push({
        type: ValidationErrorType.INVALID_PARENT_POINTER,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id,
        message: '父节点不能是叶子节点'
      });
      return;
    }

    const internalParent = parent as InternalNode;
    if (!internalParent.children.includes(node.id)) {
      errors.push({
        type: ValidationErrorType.INVALID_PARENT_POINTER,
        severity: ValidationSeverity.CRITICAL,
        nodeId: node.id,
        message: '父节点的children数组中不包含当前节点ID'
      });
    }
  }
}